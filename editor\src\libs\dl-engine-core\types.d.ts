/**
 * DL Engine Core 类型声明
 */

declare module 'dl-engine-core/utils/PerformanceMonitor' {
  export enum PerformanceMetricType {
    FPS = 'fps',
    RENDER_TIME = 'renderTime',
    PHYSICS_TIME = 'physicsTime',
    ANIMATION_TIME = 'animationTime',
    INPUT_TIME = 'inputTime',
    NETWORK_TIME = 'networkTime',
    SCRIPT_TIME = 'scriptTime',
    TOTAL_UPDATE_TIME = 'totalUpdateTime',
    MEMORY_USAGE = 'memoryUsage',
    TEXTURE_MEMORY = 'textureMemory',
    GEOMETRY_MEMORY = 'geometryMemory',
    DRAW_CALLS = 'drawCalls',
    TRIANGLES = 'triangles',
    VERTICES = 'vertices',
    COLLISION_PAIRS = 'collisionPairs',
    CONTACT_POINTS = 'contactPoints',
    ENTITY_COUNT = 'entityCount',
    COMPONENT_COUNT = 'componentCount',
    SYSTEM_COUNT = 'systemCount',
    AUDIO_SOURCE_COUNT = 'audioSourceCount',
    LIGHT_COUNT = 'lightCount',
    CAMERA_COUNT = 'cameraCount',
    PARTICLE_COUNT = 'particleCount',
    NETWORK_CONNECTION_COUNT = 'networkConnectionCount',
    NETWORK_MESSAGE_COUNT = 'networkMessageCount',
    RESOURCE_COUNT = 'resourceCount',
    LOADING_RESOURCE_COUNT = 'loadingResourceCount',
    ERROR_RESOURCE_COUNT = 'errorResourceCount',
    EVENT_COUNT = 'eventCount',
    GC_COUNT = 'gcCount',
    GC_TIME = 'gcTime',
    GPU_USAGE = 'gpuUsage',
    CPU_USAGE = 'cpuUsage',
    NETWORK_LATENCY = 'networkLatency',
    NETWORK_BANDWIDTH = 'networkBandwidth'
  }

  export interface PerformanceMetric {
    type: PerformanceMetricType;
    name: string;
    value: number;
    min: number;
    max: number;
    average: number;
    history: number[];
    historyLimit: number;
    unit?: string;
    threshold?: number;
    exceedsThreshold?: boolean;
  }

  export interface PerformanceReport {
    timestamp: number;
    metrics: Map<PerformanceMetricType, PerformanceMetric>;
    overallScore: number;
    bottlenecks: string[];
    suggestions: string[];
  }

  export interface PerformanceMonitorConfig {
    enabled?: boolean;
    sampleInterval?: number;
    historyLimit?: number;
    autoSample?: boolean;
    debug?: boolean;
    enableWarnings?: boolean;
    collectRenderMetrics?: boolean;
    collectPhysicsMetrics?: boolean;
    collectMemoryMetrics?: boolean;
    collectSystemMetrics?: boolean;
    collectGPUMetrics?: boolean;
    collectCPUMetrics?: boolean;
    collectNetworkMetrics?: boolean;
    collectResourceMetrics?: boolean;
    collectEventMetrics?: boolean;
    collectGCMetrics?: boolean;
  }

  export class PerformanceMonitor {
    static getInstance(): PerformanceMonitor;
    configure(config: Partial<PerformanceMonitorConfig>): void;
    start(): void;
    stop(): void;
    getReport(): PerformanceReport;
    updateMetric(type: PerformanceMetricType, value: number): void;
    sample(): void;
  }
}

declare module 'dl-engine-core/debug/SceneOptimizer' {
  export enum OptimizationSuggestionType {
    REDUCE_DRAW_CALLS = 'reduceDrawCalls',
    REDUCE_TRIANGLES = 'reduceTriangles',
    OPTIMIZE_TEXTURES = 'optimizeTextures',
    OPTIMIZE_MATERIALS = 'optimizeMaterials',
    ENABLE_INSTANCING = 'enableInstancing',
    ENABLE_BATCHING = 'enableBatching',
    ENABLE_LOD = 'enableLOD',
    ENABLE_OCCLUSION_CULLING = 'enableOcclusionCulling',
    OPTIMIZE_LIGHTING = 'optimizeLighting',
    OPTIMIZE_SHADOWS = 'optimizeShadows',
    OPTIMIZE_PARTICLES = 'optimizeParticles',
    OPTIMIZE_ANIMATIONS = 'optimizeAnimations',
    OPTIMIZE_MEMORY = 'optimizeMemory'
  }

  export interface OptimizationSuggestion {
    type: OptimizationSuggestionType;
    title: string;
    description: string;
    severity: number;
    expectedImprovement: number;
    implementationDifficulty: number;
    canAutoOptimize: boolean;
    autoOptimize?: () => Promise<boolean>;
    details?: any;
  }

  export interface SceneStats {
    entityCount: number;
    componentCount: number;
    renderObjectCount: number;
    lightCount: number;
    materialCount: number;
    textureCount: number;
    geometryCount: number;
    triangleCount: number;
    vertexCount: number;
    drawCallCount: number;
    memoryUsage: number;
    textureMemoryUsage: number;
    geometryMemoryUsage: number;
  }

  export interface SceneAnalysisResult {
    sceneId: string;
    sceneName: string;
    stats: SceneStats;
    suggestions: OptimizationSuggestion[];
    overallScore: number;
    timestamp: number;
  }

  export interface SceneOptimizerConfig {
    enableAutoLOD?: boolean;
    enableAutoInstancing?: boolean;
    enableAutoBatching?: boolean;
    enableAutoMaterialOptimization?: boolean;
    enableAutoTextureOptimization?: boolean;
    enableAutoGeometryOptimization?: boolean;
    enableAutoLightOptimization?: boolean;
    enableAutoShadowOptimization?: boolean;
    enableAutoMemoryOptimization?: boolean;
    enableOcclusionCulling?: boolean;
    enableDetailTexture?: boolean;
    enableMeshSimplification?: boolean;
    enableParticleOptimization?: boolean;
    enableAnimationOptimization?: boolean;
    thresholds?: {
      triangles?: { low: number; medium: number; high: number };
      drawCalls?: { low: number; medium: number; high: number };
      memory?: { low: number; medium: number; high: number };
      particles?: { low: number; medium: number; high: number };
      animations?: { low: number; medium: number; high: number };
      textureResolution?: { low: number; medium: number; high: number };
    };
    debug?: boolean;
  }

  export class SceneOptimizer {
    config: SceneOptimizerConfig;
    static getInstance(): SceneOptimizer;
    configure(config: Partial<SceneOptimizerConfig>): void;
    analyzeScene(scene?: any): Promise<SceneAnalysisResult>;
    optimizeScene(scene?: any): Promise<boolean>;
    getLastAnalysisResult(): SceneAnalysisResult | null;
  }
}
