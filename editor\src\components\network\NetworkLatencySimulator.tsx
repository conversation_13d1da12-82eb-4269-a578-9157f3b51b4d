/**
 * 网络延迟模拟器组件
 * 用于模拟各种网络延迟场景
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Form, SliderNumber, Switch, Button, Row, Col, Divider, Tooltip, Alert, Space, Select, Progress, Modal, Steps, Typography} from 'antd';
import {
  InfoCircleOutlined,
  WarningOutlined,
  ReloadOutlined,
  SaveOutlined,
  UndoOutlined,
  ExperimentOutlined,
  DisconnectOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SaveOutlined as SaveFilled,
  LoadingOutlined,
  ClockCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
// 移除引擎直接导入
import './NetworkLatencySimulator.css';

const { Option } = Select;
const { Step } = Steps;
const { Text, Paragraph, Title } = Typography;

// 预设场景
interface ScenarioConfig {
  /** 场景名称 */
  name: string;
  /** 场景描述 */
  description: string;
  /** 场景步骤 */
  steps: {
    /** 步骤名称 */
    name: string;
    /** 步骤持续时间（毫秒） */
    duration: number;
    /** 网络配置 */
    config: NetworkSimulatorConfig;
  }[];
}

// 预设场景列表
const SCENARIOS: Record<string, ScenarioConfig> = {
  networkDegradation: {
    name: '网络逐渐恶化',
    description: '模拟网络从良好状态逐渐恶化到很差的状态',
    steps: [
      {
        name: '良好网络',
        duration: 10000,
        config: {
          enabled: true,
          latency: 50,
          latencyJitter: 10,
          packetLoss: 0.01,
          bandwidthLimit: 1000000,
          enableRandomDisconnect: false}
      },
      {
        name: '轻微延迟增加',
        duration: 10000,
        config: {
          enabled: true,
          latency: 100,
          latencyJitter: 20,
          packetLoss: 0.02,
          bandwidthLimit: 800000,
          enableRandomDisconnect: false}
      },
      {
        name: '中度网络问题',
        duration: 10000,
        config: {
          enabled: true,
          latency: 200,
          latencyJitter: 50,
          packetLoss: 0.05,
          bandwidthLimit: 500000,
          enableRandomDisconnect: false}
      },
      {
        name: '严重网络问题',
        duration: 10000,
        config: {
          enabled: true,
          latency: 350,
          latencyJitter: 100,
          packetLoss: 0.1,
          bandwidthLimit: 200000,
          enableRandomDisconnect: true,
          disconnectProbability: 0.01,
          reconnectTime: 2000}
      }
    ]
  },
  networkRecovery: {
    name: '网络逐渐恢复',
    description: '模拟网络从很差状态逐渐恢复到良好状态',
    steps: [
      {
        name: '严重网络问题',
        duration: 10000,
        config: {
          enabled: true,
          latency: 350,
          latencyJitter: 100,
          packetLoss: 0.1,
          bandwidthLimit: 200000,
          enableRandomDisconnect: true,
          disconnectProbability: 0.01,
          reconnectTime: 2000}
      },
      {
        name: '中度网络问题',
        duration: 10000,
        config: {
          enabled: true,
          latency: 200,
          latencyJitter: 50,
          packetLoss: 0.05,
          bandwidthLimit: 500000,
          enableRandomDisconnect: false}
      },
      {
        name: '轻微延迟',
        duration: 10000,
        config: {
          enabled: true,
          latency: 100,
          latencyJitter: 20,
          packetLoss: 0.02,
          bandwidthLimit: 800000,
          enableRandomDisconnect: false}
      },
      {
        name: '良好网络',
        duration: 10000,
        config: {
          enabled: true,
          latency: 50,
          latencyJitter: 10,
          packetLoss: 0.01,
          bandwidthLimit: 1000000,
          enableRandomDisconnect: false}
      }
    ]
  },
  networkFluctuation: {
    name: '网络波动',
    description: '模拟网络质量不稳定，时好时坏',
    steps: [
      {
        name: '良好网络',
        duration: 8000,
        config: {
          enabled: true,
          latency: 50,
          latencyJitter: 10,
          packetLoss: 0.01,
          bandwidthLimit: 1000000,
          enableRandomDisconnect: false}
      },
      {
        name: '突然延迟增加',
        duration: 5000,
        config: {
          enabled: true,
          latency: 300,
          latencyJitter: 80,
          packetLoss: 0.08,
          bandwidthLimit: 300000,
          enableRandomDisconnect: false}
      },
      {
        name: '部分恢复',
        duration: 8000,
        config: {
          enabled: true,
          latency: 120,
          latencyJitter: 30,
          packetLoss: 0.03,
          bandwidthLimit: 700000,
          enableRandomDisconnect: false}
      },
      {
        name: '再次恶化',
        duration: 5000,
        config: {
          enabled: true,
          latency: 250,
          latencyJitter: 70,
          packetLoss: 0.07,
          bandwidthLimit: 400000,
          enableRandomDisconnect: false}
      },
      {
        name: '完全恢复',
        duration: 8000,
        config: {
          enabled: true,
          latency: 50,
          latencyJitter: 10,
          packetLoss: 0.01,
          bandwidthLimit: 1000000,
          enableRandomDisconnect: false}
      }
    ]
  },
  disconnectionScenario: {
    name: '断线重连场景',
    description: '模拟网络断线后自动重连的场景',
    steps: [
      {
        name: '正常连接',
        duration: 10000,
        config: {
          enabled: true,
          latency: 80,
          latencyJitter: 20,
          packetLoss: 0.02,
          bandwidthLimit: 800000,
          enableRandomDisconnect: false}
      },
      {
        name: '连接不稳定',
        duration: 8000,
        config: {
          enabled: true,
          latency: 150,
          latencyJitter: 50,
          packetLoss: 0.05,
          bandwidthLimit: 500000,
          enableRandomDisconnect: true,
          disconnectProbability: 0.005,
          reconnectTime: 1500}
      },
      {
        name: '断线',
        duration: 5000,
        config: {
          enabled: true,
          latency: 0,
          latencyJitter: 0,
          packetLoss: 1.0, // 100%丢包表示断线
          bandwidthLimit: 0,
          enableRandomDisconnect: false}
      },
      {
        name: '重连中',
        duration: 8000,
        config: {
          enabled: true,
          latency: 200,
          latencyJitter: 80,
          packetLoss: 0.2,
          bandwidthLimit: 200000,
          enableRandomDisconnect: false}
      },
      {
        name: '恢复连接',
        duration: 10000,
        config: {
          enabled: true,
          latency: 100,
          latencyJitter: 30,
          packetLoss: 0.03,
          bandwidthLimit: 700000,
          enableRandomDisconnect: false}
      }
    ]
  }
};

interface NetworkLatencySimulatorProps {
  /** 当前配置 */
  config: NetworkSimulatorConfig;
  /** 配置变更回调 */
  onConfigChange?: (config: NetworkSimulatorConfig) => void;
  /** 模拟断线回调 */
  onSimulateDisconnect?: () => void;
  /** 模拟重连回调 */
  onSimulateReconnect?: () => void;
  /** 是否正在加载 */
  loading?: boolean;
}

/**
 * 网络延迟模拟器组件
 */
const NetworkLatencySimulator: React.FC<NetworkLatencySimulatorProps> = ({
  config,
  onConfigChange,
  onSimulateDisconnect,
  onSimulateReconnect,
  loading = false}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [localConfig, setLocalConfig] = useState<NetworkSimulatorConfig>(config);
  const [scenarioModalVisible, setScenarioModalVisible] = useState<boolean>(false);
  const [selectedScenario, setSelectedScenario] = useState<string>('');
  const [runningScenario, setRunningScenario] = useState<boolean>(false);
  const [currentScenarioStep, setCurrentScenarioStep] = useState<number>(0);
  const [scenarioProgress, setScenarioProgress] = useState<number>(0);
  const scenarioTimerRef = useRef<number | null>(null);
  const stepTimerRef = useRef<number | null>(null);
  const progressIntervalRef = useRef<number | null>(null);

  // 初始化表单
  useEffect(() => {
    form.setFieldsValue(config);
    setLocalConfig(config);
  }, [config, form]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scenarioTimerRef.current) {
        clearTimeout(scenarioTimerRef.current);
      }
      if (stepTimerRef.current) {
        clearTimeout(stepTimerRef.current);
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  // 处理表单变更
  const handleFormChange = () => {
    const values = form.getFieldsValue();
    setLocalConfig(values);
  };

  // 应用配置
  const handleApplyConfig = () => {
    if (onConfigChange) {
      onConfigChange(localConfig);
    }
  };

  // 重置配置
  const handleResetConfig = () => {
    form.setFieldsValue(config);
    setLocalConfig(config);
  };

  // 打开场景模拟对话框
  const handleOpenScenarioModal = () => {
    setScenarioModalVisible(true);
  };

  // 开始场景模拟
  const handleStartScenario = () => {
    if (!selectedScenario || !SCENARIOS[selectedScenario]) {
      return;
    }

    setRunningScenario(true);
    setCurrentScenarioStep(0);
    setScenarioProgress(0);
    setScenarioModalVisible(false);

    const scenario = SCENARIOS[selectedScenario];
    runScenarioStep(scenario, 0);
  };

  // 运行场景步骤
  const runScenarioStep = (scenario: ScenarioConfig, stepIndex: number) => {
    if (stepIndex >= scenario.steps.length) {
      // 场景结束
      setRunningScenario(false);
      return;
    }

    const step = scenario.steps[stepIndex];
    setCurrentScenarioStep(stepIndex);

    // 应用步骤配置
    if (onConfigChange) {
      onConfigChange(step.config);
    }

    // 更新进度
    let startTime = Date.now();
    let stepDuration = step.duration;
    
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }
    
    progressIntervalRef.current = window.setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(100, (elapsed / stepDuration) * 100);
      setScenarioProgress(progress);
      
      if (progress >= 100 && progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    }, 100);

    // 设置下一步的定时器
    stepTimerRef.current = window.setTimeout(() => {
      runScenarioStep(scenario, stepIndex + 1);
    }, step.duration);
  };

  // 停止场景模拟
  const handleStopScenario = () => {
    if (stepTimerRef.current) {
      clearTimeout(stepTimerRef.current);
      stepTimerRef.current = null;
    }
    
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
    
    setRunningScenario(false);
    
    // 恢复原始配置
    if (onConfigChange) {
      onConfigChange(config);
    }
  };

  return (
    <div className="network-latency-simulator">
      <Card
        title={
          <Space>
            <ExperimentOutlined />
            {t('network.simulator.title')}
          </Space>
        }
        extra={
          <Space>
            {runningScenario ? (
              <Button
                icon={<StopOutlined />}
                onClick={handleStopScenario}
                danger
              >
                {t('network.simulator.stopScenario')}
              </Button>
            ) : (
              <Button
                icon={<PlayCircleOutlined />}
                onClick={handleOpenScenarioModal}
              >
                {t('network.simulator.runScenario')}
              </Button>
            )}
          </Space>
        }
      >
        {runningScenario && (
          <div className="scenario-progress">
            <div className="scenario-info">
              <Text strong>{SCENARIOS[selectedScenario].name}</Text>
              <Text type="secondary">
                {t('network.simulator.step')} {currentScenarioStep + 1}/{SCENARIOS[selectedScenario].steps.length}:
                {SCENARIOS[selectedScenario].steps[currentScenarioStep].name}
              </Text>
            </div>
            <Progress percent={Math.round(scenarioProgress)} status="active" />
          </div>
        )}

        <Alert
          message={t('network.simulator.warning')}
          description={t('network.simulator.warningDescription')}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleFormChange}
          disabled={runningScenario || loading}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="enabled"
                label={t('network.simulator.enabled')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Divider>{t('network.simulator.latencySettings')}</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="latency"
                label={
                  <span>
                    {t('network.simulator.latency')}
                    <Tooltip title={t('network.simulator.latencyTooltip')}>
                      <InfoCircleOutlined style={{ marginLeft: 8 }} />
                    </Tooltip>
                  </span>
                }
              >
                <Slider
                  min={0}
                  max={1000}
                  step={10}
                  marks={{
                    0: '0ms',
                    100: '100ms',
                    300: '300ms',
                    600: '600ms',
                    1000: '1000ms'}}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="latencyJitter"
                label={
                  <span>
                    {t('network.simulator.jitter')}
                    <Tooltip title={t('network.simulator.jitterTooltip')}>
                      <InfoCircleOutlined style={{ marginLeft: 8 }} />
                    </Tooltip>
                  </span>
                }
              >
                <Slider
                  min={0}
                  max={200}
                  step={5}
                  marks={{
                    0: '0ms',
                    50: '50ms',
                    100: '100ms',
                    200: '200ms'}}
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider>{t('network.simulator.packetLossSettings')}</Divider>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="packetLoss"
                label={
                  <span>
                    {t('network.simulator.packetLoss')}
                    <Tooltip title={t('network.simulator.packetLossTooltip')}>
                      <InfoCircleOutlined style={{ marginLeft: 8 }} />
                    </Tooltip>
                  </span>
                }
              >
                <Slider
                  min={0}
                  max={0.3}
                  step={0.01}
                  tipFormatter={value => `${(value * 100).toFixed(0)}%`}
                  marks={{
                    0: '0%',
                    0.05: '5%',
                    0.1: '10%',
                    0.2: '20%',
                    0.3: '30%'}}
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider>{t('network.simulator.bandwidthSettings')}</Divider>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="bandwidthLimit"
                label={
                  <span>
                    {t('network.simulator.bandwidth')}
                    <Tooltip title={t('network.simulator.bandwidthTooltip')}>
                      <InfoCircleOutlined style={{ marginLeft: 8 }} />
                    </Tooltip>
                  </span>
                }
              >
                <Slider
                  min={0}
                  max={2000000}
                  step={100000}
                  tipFormatter={value => value === 0 ? t('network.simulator.unlimited') : `${(value / 1024).toFixed(0)} KB/s`}
                  marks={{
                    0: t('network.simulator.unlimited'),
                    500000: '500 KB/s',
                    1000000: '1 MB/s',
                    2000000: '2 MB/s'}}
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider>{t('network.simulator.disconnectionSettings')}</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableRandomDisconnect"
                label={t('network.simulator.enableRandomDisconnect')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="disconnectProbability"
                label={t('network.simulator.disconnectProbability')}
              >
                <Slider
                  min={0}
                  max={0.05}
                  step={0.001}
                  tipFormatter={value => `${(value * 100).toFixed(1)}%`}
                  marks={{
                    0: '0%',
                    0.01: '1%',
                    0.03: '3%',
                    0.05: '5%'}}
                  disabled={!form.getFieldValue('enableRandomDisconnect')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="reconnectTime"
                label={t('network.simulator.reconnectTime')}
              >
                <Slider
                  min={1000}
                  max={10000}
                  step={500}
                  tipFormatter={value => `${(value / 1000).toFixed(1)}s`}
                  marks={{
                    1000: '1s',
                    3000: '3s',
                    5000: '5s',
                    10000: '10s'}}
                  disabled={!form.getFieldValue('enableRandomDisconnect')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <Row gutter={16}>
            <Col span={24} style={{ textAlign: 'right' }}>
              <Space>
                <Button
                  icon={<UndoOutlined />}
                  onClick={handleResetConfig}
                  disabled={runningScenario || loading}
                >
                  {t('network.simulator.reset')}
                </Button>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleApplyConfig}
                  loading={loading}
                  disabled={runningScenario}
                >
                  {t('network.simulator.apply')}
                </Button>
                <Button
                  danger
                  icon={<DisconnectOutlined />}
                  onClick={onSimulateDisconnect}
                  disabled={runningScenario || loading}
                >
                  {t('network.simulator.simulateDisconnect')}
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={onSimulateReconnect}
                  disabled={runningScenario || loading}
                >
                  {t('network.simulator.simulateReconnect')}
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>

        <Modal
          title={t('network.simulator.selectScenario')}
          open={scenarioModalVisible}
          onCancel={() => setScenarioModalVisible(false)}
          footer={[
            <Button key="cancel" onClick={() => setScenarioModalVisible(false)}>
              {t('network.simulator.cancel')}
            </Button>,
            <Button
              key="start"
              type="primary"
              disabled={!selectedScenario}
              onClick={handleStartScenario}
            >
              {t('network.simulator.startScenario')}
            </Button>,
          ]}
        >
          <div className="scenario-selection">
            <Paragraph>{t('network.simulator.scenarioDescription')}</Paragraph>
            
            <Form layout="vertical">
              <Form.Item
                label={t('network.simulator.scenario')}
                required
              >
                <Select
                  placeholder={t('network.simulator.selectScenarioPlaceholder')}
                  value={selectedScenario}
                  onChange={setSelectedScenario}
                  style={{ width: '100%' }}
                >
                  {Object.entries(SCENARIOS).map(([key, scenario]) => (
                    <Option key={key} value={key}>
                      {scenario.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Form>
            
            {selectedScenario && (
              <div className="scenario-details">
                <Title level={5}>{SCENARIOS[selectedScenario].name}</Title>
                <Paragraph>{SCENARIOS[selectedScenario].description}</Paragraph>
                
                <Steps direction="vertical" size="small">
                  {SCENARIOS[selectedScenario].steps.map((step, index) => (
                    <Step
                      key={index}
                      title={step.name}
                      description={
                        <div>
                          <div>{t('network.simulator.duration')}: {(step.duration / 1000).toFixed(1)}s</div>
                          <div>{t('network.simulator.latency')}: {step.config.latency}ms</div>
                          <div>{t('network.simulator.packetLoss')}: {(step.config.packetLoss * 100).toFixed(1)}%</div>
                        </div>
                      }
                    />
                  ))}
                </Steps>
              </div>
            )}
          </div>
        </Modal>
      </Card>
    </div>
  );
};

export default NetworkLatencySimulator;
