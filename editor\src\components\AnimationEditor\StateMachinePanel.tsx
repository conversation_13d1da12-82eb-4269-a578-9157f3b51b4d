/**
 * 状态机面板组件
 * 用于显示和编辑状态机的属性
 */
import React, { useState, useEffect } from 'react';
import { Tabs, Form, Input, Select, Switch, InputNumber, Button, Collapse, Table, Space, Tooltip, message } from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  SaveOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch } from '../../store';
import { AnimationStateMachineData } from '../../libs/dl-engine';
import {
  updateState,
  updateTransition,
  removeParameter,
  updateParameter,
  setEditingMode
} from '../../store/animations/stateMachineSlice';
import { stateMachineService } from '../../services/stateMachineService';
import './StateMachineEditor.less';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;

/**
 * 状态机面板属性
 */
interface StateMachinePanelProps {
  /** 状态机数据 */
  stateMachine: AnimationStateMachineData | null;
  /** 选中的状态名称 */
  selectedState: string | null;
  /** 选中的转换规则 */
  selectedTransition: { from: string; to: string } | null;
  /** 实体ID */
  entityId: string;
  /** 可用的动画片段 */
  availableClips: string[];
}

/**
 * 状态机面板组件
 */
const StateMachinePanel: React.FC<StateMachinePanelProps> = ({
  stateMachine,
  selectedState,
  selectedTransition,
  entityId,
  availableClips
}) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  
  // 状态
  const [activeTab, setActiveTab] = useState('properties');
  const [stateForm] = Form.useForm();
  const [transitionForm] = Form.useForm();
  const [parameterForm] = Form.useForm();
  const [editingParameter, setEditingParameter] = useState<string | null>(null);
  
  // 当选中的状态变化时，更新表单
  useEffect(() => {
    if (selectedState && stateMachine) {
      const state = stateMachine.states.find(s => s.name === selectedState);
      if (state) {
        stateForm.setFieldsValue({
          name: state.name,
          type: state.type,
          clipName: state.clipName,
          loop: state.loop,
          clamp: state.clamp,
          parameterName: state.parameterName,
          blendSpaceType: state.blendSpaceType
        });
      }
    } else {
      stateForm.resetFields();
    }
  }, [selectedState, stateMachine, stateForm]);
  
  // 当选中的转换变化时，更新表单
  useEffect(() => {
    if (selectedTransition && stateMachine) {
      const transition = stateMachine.transitions.find(
        t => t.from === selectedTransition.from && t.to === selectedTransition.to
      );
      if (transition) {
        transitionForm.setFieldsValue({
          from: transition.from,
          to: transition.to,
          conditionExpression: transition.conditionExpression,
          duration: transition.duration,
          canInterrupt: transition.canInterrupt,
          curveType: transition.curveType,
          priority: transition.priority
        });
      }
    } else {
      transitionForm.resetFields();
    }
  }, [selectedTransition, stateMachine, transitionForm]);
  
  // 处理状态表单提交
  const handleStateFormSubmit = (values: any) => {
    if (!selectedState) return;
    
    dispatch(updateState({
      name: selectedState,
      state: {
        type: values.type,
        clipName: values.clipName,
        loop: values.loop,
        clamp: values.clamp,
        parameterName: values.parameterName,
        blendSpaceType: values.blendSpaceType
      }
    }));
    
    dispatch(setEditingMode(true));
    message.success(t('editor.animation.stateUpdated'));
  };
  
  // 处理转换表单提交
  const handleTransitionFormSubmit = (values: any) => {
    if (!selectedTransition) return;
    
    dispatch(updateTransition({
      from: selectedTransition.from,
      to: selectedTransition.to,
      transition: {
        conditionExpression: values.conditionExpression,
        duration: values.duration,
        canInterrupt: values.canInterrupt,
        curveType: values.curveType,
        priority: values.priority
      }
    }));
    
    dispatch(setEditingMode(true));
    message.success(t('editor.animation.transitionUpdated'));
  };
  
  // 处理参数表单提交
  const handleParameterFormSubmit = (values: any) => {
    if (!editingParameter) return;
    
    dispatch(updateParameter({
      name: editingParameter,
      parameter: {
        type: values.type,
        defaultValue: values.defaultValue,
        minValue: values.minValue,
        maxValue: values.maxValue
      }
    }));
    
    dispatch(setEditingMode(true));
    setEditingParameter(null);
    message.success(t('editor.animation.parameterUpdated'));
  };
  
  // 处理参数编辑
  const handleEditParameter = (name: string) => {
    if (!stateMachine) return;
    
    const parameter = stateMachine.parameters.find(p => p.name === name);
    if (!parameter) return;
    
    parameterForm.setFieldsValue({
      name: parameter.name,
      type: parameter.type,
      defaultValue: parameter.defaultValue,
      minValue: parameter.minValue,
      maxValue: parameter.maxValue
    });
    
    setEditingParameter(name);
  };
  
  // 处理参数删除
  const handleDeleteParameter = (name: string) => {
    dispatch(removeParameter(name));
    dispatch(setEditingMode(true));
    message.success(t('editor.animation.parameterDeleted'));
  };
  
  // 处理参数值变化
  const handleParameterValueChange = async (name: string, value: any) => {
    try {
      await stateMachineService.setStateMachineParameter(entityId, name, value);
    } catch (error) {
      message.error(t('editor.animation.setParameterFailed'));
    }
  };
  
  // 渲染状态属性
  const renderStateProperties = () => {
    if (!selectedState || !stateMachine) {
      return (
        <div className="no-selection-message">
          {t('editor.animation.noStateSelected')}
        </div>
      );
    }
    
    const state = stateMachine.states.find(s => s.name === selectedState);
    if (!state) return null;
    
    return (
      <Form
        form={stateForm}
        layout="vertical"
        onFinish={handleStateFormSubmit}
      >
        <Form.Item
          name="name"
          label={t('editor.animation.stateName')}
        >
          <Input disabled />
        </Form.Item>
        
        <Form.Item
          name="type"
          label={t('editor.animation.stateType')}
        >
          <Select disabled>
            <Option value="SingleAnimationState">{t('editor.animation.singleAnimationState')}</Option>
            <Option value="BlendAnimationState">{t('editor.animation.blendAnimationState')}</Option>
          </Select>
        </Form.Item>
        
        {state.type === 'SingleAnimationState' && (
          <>
            <Form.Item
              name="clipName"
              label={t('editor.animation.clipName')}
              rules={[{ required: true, message: t('editor.animation.clipNameRequired') as string }]}
            >
              <Select>
                {availableClips.map(clip => (
                  <Option key={clip} value={clip}>{clip}</Option>
                ))}
              </Select>
            </Form.Item>
            
            <Form.Item
              name="loop"
              label={t('editor.animation.loop')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            
            <Form.Item
              name="clamp"
              label={t('editor.animation.clamp')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </>
        )}
        
        {state.type === 'BlendAnimationState' && (
          <>
            <Form.Item
              name="parameterName"
              label={t('editor.animation.parameterName')}
              rules={[{ required: true, message: t('editor.animation.parameterNameRequired') as string }]}
            >
              <Select>
                {stateMachine.parameters.map(param => (
                  <Option key={param.name} value={param.name}>{param.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="blendSpaceType"
              label={t('editor.animation.blendSpaceType')}
              rules={[{ required: true, message: t('editor.animation.blendSpaceTypeRequired') as string }]}
            >
              <Select>
                <Option value="1D">{t('editor.animation.blendSpace1D')}</Option>
                <Option value="2D">{t('editor.animation.blendSpace2D')}</Option>
              </Select>
            </Form.Item>
          </>
        )}
        
        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
            {t('editor.save')}
          </Button>
        </Form.Item>
      </Form>
    );
  };
  
  // 渲染转换属性
  const renderTransitionProperties = () => {
    if (!selectedTransition || !stateMachine) {
      return (
        <div className="no-selection-message">
          {t('editor.animation.noTransitionSelected')}
        </div>
      );
    }
    
    const transition = stateMachine.transitions.find(
      t => t.from === selectedTransition.from && t.to === selectedTransition.to
    );
    if (!transition) return null;
    
    return (
      <Form
        form={transitionForm}
        layout="vertical"
        onFinish={handleTransitionFormSubmit}
      >
        <Form.Item
          name="from"
          label={t('editor.animation.fromState')}
        >
          <Input disabled />
        </Form.Item>
        
        <Form.Item
          name="to"
          label={t('editor.animation.toState')}
        >
          <Input disabled />
        </Form.Item>
        
        <Form.Item
          name="conditionExpression"
          label={t('editor.animation.conditionExpression')}
          rules={[{ required: true, message: t('editor.animation.conditionExpressionRequired') as string }]}
          tooltip={t('editor.animation.conditionExpressionTooltip') as string}
        >
          <Input.TextArea rows={4} />
        </Form.Item>

        <Form.Item
          name="duration"
          label={t('editor.animation.duration')}
          rules={[{ required: true, message: t('editor.animation.durationRequired') as string }]}
        >
          <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
        </Form.Item>
        
        <Form.Item
          name="canInterrupt"
          label={t('editor.animation.canInterrupt')}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
        
        <Form.Item
          name="curveType"
          label={t('editor.animation.curveType')}
        >
          <Select>
            <Option value="bezier">{t('editor.animation.bezier')}</Option>
            <Option value="straight">{t('editor.animation.straight')}</Option>
            <Option value="arc">{t('editor.animation.arc')}</Option>
            <Option value="dashed">{t('editor.animation.dashed')}</Option>
          </Select>
        </Form.Item>
        
        <Form.Item
          name="priority"
          label={t('editor.animation.priority')}
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>
        
        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
            {t('editor.save')}
          </Button>
        </Form.Item>
      </Form>
    );
  };
  
  // 渲染参数列表
  const renderParameters = () => {
    if (!stateMachine) {
      return (
        <div className="no-selection-message">
          {t('editor.animation.noStateMachine')}
        </div>
      );
    }
    
    const columns = [
      {
        title: t('editor.animation.parameterName'),
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: t('editor.animation.parameterType'),
        dataIndex: 'type',
        key: 'type'
      },
      {
        title: t('editor.animation.parameterValue'),
        dataIndex: 'defaultValue',
        key: 'defaultValue',
        render: (value: any, record: any) => {
          if (record.type === 'number') {
            return (
              <InputNumber
                value={value}
                min={record.minValue}
                max={record.maxValue}
                onChange={(val) => handleParameterValueChange(record.name, val)}
                style={{ width: '100%' }}
              />
            );
          } else if (record.type === 'boolean') {
            return (
              <Switch
                checked={value}
                onChange={(val) => handleParameterValueChange(record.name, val)}
              />
            );
          } else {
            return (
              <Input
                value={value}
                onChange={(e) => handleParameterValueChange(record.name, e.target.value)}
              />
            );
          }
        }
      },
      {
        title: t('editor.actions'),
        key: 'actions',
        render: (_: any, record: any) => (
          <Space>
            <Tooltip title={t('editor.edit')}>
              <Button
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEditParameter(record.name)}
              />
            </Tooltip>
            <Tooltip title={t('editor.delete')}>
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
                onClick={() => handleDeleteParameter(record.name)}
              />
            </Tooltip>
          </Space>
        )
      }
    ];
    
    return (
      <div className="parameters-container">
        <Table
          dataSource={stateMachine.parameters}
          columns={columns}
          rowKey="name"
          size="small"
          pagination={false}
        />
        
        {editingParameter && (
          <div className="parameter-form">
            <h3>{t('editor.animation.editParameter')}</h3>
            <Form
              form={parameterForm}
              layout="vertical"
              onFinish={handleParameterFormSubmit}
            >
              <Form.Item
                name="name"
                label={t('editor.animation.parameterName')}
              >
                <Input disabled />
              </Form.Item>
              
              <Form.Item
                name="type"
                label={t('editor.animation.parameterType')}
              >
                <Select disabled>
                  <Option value="number">{t('editor.animation.number')}</Option>
                  <Option value="boolean">{t('editor.animation.boolean')}</Option>
                  <Option value="string">{t('editor.animation.string')}</Option>
                  <Option value="vector2">{t('editor.animation.vector2')}</Option>
                  <Option value="vector3">{t('editor.animation.vector3')}</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="defaultValue"
                label={t('editor.animation.defaultValue')}
              >
                <Input />
              </Form.Item>
              
              {parameterForm.getFieldValue('type') === 'number' && (
                <>
                  <Form.Item
                    name="minValue"
                    label={t('editor.animation.minValue')}
                  >
                    <InputNumber style={{ width: '100%' }} />
                  </Form.Item>
                  
                  <Form.Item
                    name="maxValue"
                    label={t('editor.animation.maxValue')}
                  >
                    <InputNumber style={{ width: '100%' }} />
                  </Form.Item>
                </>
              )}
              
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                    {t('editor.save')}
                  </Button>
                  <Button onClick={() => setEditingParameter(null)}>
                    {t('editor.cancel')}
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </div>
    );
  };
  
  return (
    <div className="state-machine-panel">
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={t('editor.animation.properties')} key="properties">
          <Collapse defaultActiveKey={['state', 'transition']}>
            <Panel header={t('editor.animation.stateProperties')} key="state">
              {renderStateProperties()}
            </Panel>
            <Panel header={t('editor.animation.transitionProperties')} key="transition">
              {renderTransitionProperties()}
            </Panel>
          </Collapse>
        </TabPane>
        <TabPane tab={t('editor.animation.parameters')} key="parameters">
          {renderParameters()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default StateMachinePanel;
