/**
 * DL Engine Core 模块测试
 */

import { PerformanceMonitor, PerformanceMetricType } from '../utils/PerformanceMonitor';
import { SceneOptimizer, OptimizationSuggestionType } from '../debug/SceneOptimizer';

describe('PerformanceMonitor', () => {
  let monitor: PerformanceMonitor;

  beforeEach(() => {
    monitor = PerformanceMonitor.getInstance();
  });

  afterEach(() => {
    monitor.stop();
  });

  test('应该能够获取单例实例', () => {
    const instance1 = PerformanceMonitor.getInstance();
    const instance2 = PerformanceMonitor.getInstance();
    expect(instance1).toBe(instance2);
  });

  test('应该能够配置监控器', () => {
    const config = {
      enabled: true,
      sampleInterval: 500,
      historyLimit: 30,
      debug: true
    };

    monitor.configure(config);
    expect(monitor).toBeDefined();
  });

  test('应该能够启动和停止监控', () => {
    monitor.configure({ enabled: true });
    monitor.start();
    
    // 监控应该正在运行
    expect(monitor).toBeDefined();
    
    monitor.stop();
    // 监控应该已停止
    expect(monitor).toBeDefined();
  });

  test('应该能够获取性能报告', () => {
    monitor.configure({ enabled: true });
    monitor.start();
    
    const report = monitor.getReport();
    
    expect(report).toBeDefined();
    expect(report.timestamp).toBeGreaterThan(0);
    expect(report.metrics).toBeInstanceOf(Map);
    expect(report.overallScore).toBeGreaterThanOrEqual(0);
    expect(report.overallScore).toBeLessThanOrEqual(100);
    expect(Array.isArray(report.bottlenecks)).toBe(true);
    expect(Array.isArray(report.suggestions)).toBe(true);
  });

  test('应该能够更新指标', () => {
    monitor.configure({ enabled: true });
    monitor.start();
    
    monitor.updateMetric(PerformanceMetricType.FPS, 60);
    
    const report = monitor.getReport();
    const fpsMetric = report.metrics.get(PerformanceMetricType.FPS);
    
    expect(fpsMetric).toBeDefined();
    expect(fpsMetric!.value).toBe(60);
  });

  test('应该能够采样性能数据', () => {
    monitor.configure({ enabled: true });
    monitor.start();
    
    monitor.sample();
    
    const report = monitor.getReport();
    expect(report.metrics.size).toBeGreaterThan(0);
  });
});

describe('SceneOptimizer', () => {
  let optimizer: SceneOptimizer;

  beforeEach(() => {
    optimizer = SceneOptimizer.getInstance();
  });

  test('应该能够获取单例实例', () => {
    const instance1 = SceneOptimizer.getInstance();
    const instance2 = SceneOptimizer.getInstance();
    expect(instance1).toBe(instance2);
  });

  test('应该能够配置优化器', () => {
    const config = {
      enableAutoLOD: true,
      enableAutoBatching: true,
      debug: true,
      thresholds: {
        triangles: { low: 50000, medium: 250000, high: 500000 }
      }
    };

    optimizer.configure(config);
    expect(optimizer.config.enableAutoLOD).toBe(true);
    expect(optimizer.config.enableAutoBatching).toBe(true);
    expect(optimizer.config.debug).toBe(true);
  });

  test('应该能够分析场景', async () => {
    const mockScene = {
      id: 'test-scene',
      name: '测试场景'
    };

    const result = await optimizer.analyzeScene(mockScene);

    expect(result).toBeDefined();
    expect(result.sceneId).toBe('test-scene');
    expect(result.sceneName).toBe('测试场景');
    expect(result.stats).toBeDefined();
    expect(Array.isArray(result.suggestions)).toBe(true);
    expect(result.overallScore).toBeGreaterThanOrEqual(0);
    expect(result.overallScore).toBeLessThanOrEqual(100);
    expect(result.timestamp).toBeGreaterThan(0);
  });

  test('应该能够优化场景', async () => {
    const mockScene = {
      id: 'test-scene',
      name: '测试场景'
    };

    // 先分析场景
    await optimizer.analyzeScene(mockScene);
    
    // 然后优化场景
    const success = await optimizer.optimizeScene(mockScene);
    
    expect(typeof success).toBe('boolean');
  });

  test('应该能够获取最后的分析结果', async () => {
    const mockScene = {
      id: 'test-scene',
      name: '测试场景'
    };

    await optimizer.analyzeScene(mockScene);
    const result = optimizer.getLastAnalysisResult();

    expect(result).toBeDefined();
    expect(result!.sceneId).toBe('test-scene');
  });

  test('应该根据阈值生成优化建议', async () => {
    // 配置较低的阈值以确保生成建议
    optimizer.configure({
      thresholds: {
        triangles: { low: 1000, medium: 5000, high: 10000 },
        drawCalls: { low: 10, medium: 50, high: 100 },
        memory: { low: 10, medium: 25, high: 50 }
      }
    });

    const result = await optimizer.analyzeScene();
    
    // 应该有一些优化建议
    expect(result.suggestions.length).toBeGreaterThan(0);
    
    // 检查建议的结构
    result.suggestions.forEach(suggestion => {
      expect(suggestion.type).toBeDefined();
      expect(suggestion.title).toBeDefined();
      expect(suggestion.description).toBeDefined();
      expect(suggestion.severity).toBeGreaterThanOrEqual(0);
      expect(suggestion.severity).toBeLessThanOrEqual(1);
      expect(suggestion.expectedImprovement).toBeGreaterThanOrEqual(0);
      expect(suggestion.expectedImprovement).toBeLessThanOrEqual(1);
      expect(suggestion.implementationDifficulty).toBeGreaterThanOrEqual(0);
      expect(suggestion.implementationDifficulty).toBeLessThanOrEqual(1);
      expect(typeof suggestion.canAutoOptimize).toBe('boolean');
    });
  });
});

describe('模块集成测试', () => {
  test('性能监控器和场景优化器应该能够协同工作', async () => {
    const monitor = PerformanceMonitor.getInstance();
    const optimizer = SceneOptimizer.getInstance();

    // 启动性能监控
    monitor.configure({ enabled: true, autoSample: true });
    monitor.start();

    // 等待一些性能数据
    await new Promise(resolve => setTimeout(resolve, 100));

    // 获取性能报告
    const performanceReport = monitor.getReport();
    expect(performanceReport).toBeDefined();

    // 分析场景
    const analysisResult = await optimizer.analyzeScene({
      id: 'integration-test-scene',
      name: '集成测试场景'
    });

    expect(analysisResult).toBeDefined();

    // 清理
    monitor.stop();
  });

  test('应该能够处理错误情况', async () => {
    const optimizer = SceneOptimizer.getInstance();

    // 测试没有场景的情况
    const result = await optimizer.analyzeScene();
    expect(result.sceneId).toBe('unknown');
    expect(result.sceneName).toBe('未知场景');
  });
});
