# DL Engine Core 模块

这个目录包含了 DL Engine 的核心模块，提供了性能监控和场景优化功能。

## 模块结构

```
dl-engine-core/
├── utils/
│   └── PerformanceMonitor.ts    # 性能监控器
├── debug/
│   └── SceneOptimizer.ts        # 场景优化器
├── index.ts                     # 模块导出
└── README.md                    # 说明文档
```

## 性能监控器 (PerformanceMonitor)

性能监控器用于实时监控引擎的各项性能指标。

### 基本用法

```typescript
import { PerformanceMonitor, PerformanceMetricType } from './utils/PerformanceMonitor';

// 获取单例实例
const monitor = PerformanceMonitor.getInstance();

// 配置监控器
monitor.configure({
  enabled: true,
  sampleInterval: 1000,        // 采样间隔（毫秒）
  historyLimit: 60,           // 历史数据长度
  autoSample: true,           // 自动采样
  debug: true,                // 调试模式
  collectRenderMetrics: true, // 收集渲染指标
  collectMemoryMetrics: true, // 收集内存指标
  collectSystemMetrics: true  // 收集系统指标
});

// 启动监控
monitor.start();

// 获取性能报告
const report = monitor.getReport();
console.log('总体评分:', report.overallScore);
console.log('性能瓶颈:', report.bottlenecks);
console.log('优化建议:', report.suggestions);

// 获取特定指标
const fps = report.metrics.get(PerformanceMetricType.FPS);
const memory = report.metrics.get(PerformanceMetricType.MEMORY_USAGE);
const drawCalls = report.metrics.get(PerformanceMetricType.DRAW_CALLS);

// 停止监控
monitor.stop();
```

### 支持的性能指标

- **FPS**: 帧率
- **RENDER_TIME**: 渲染时间
- **MEMORY_USAGE**: 内存使用量
- **DRAW_CALLS**: 绘制调用次数
- **TRIANGLES**: 三角形数量
- **VERTICES**: 顶点数量
- **TEXTURE_MEMORY**: 纹理内存
- **GEOMETRY_MEMORY**: 几何体内存
- 更多指标...

## 场景优化器 (SceneOptimizer)

场景优化器用于分析场景并提供优化建议和自动优化功能。

### 基本用法

```typescript
import { SceneOptimizer } from './debug/SceneOptimizer';

// 获取单例实例
const optimizer = SceneOptimizer.getInstance();

// 配置优化器
optimizer.configure({
  enableAutoLOD: true,                    // 启用自动LOD
  enableAutoBatching: true,               // 启用自动批处理
  enableAutoTextureOptimization: true,    // 启用自动纹理优化
  enableAutoMemoryOptimization: true,     // 启用自动内存优化
  debug: true,                           // 调试模式
  thresholds: {                          // 阈值配置
    triangles: { low: 100000, medium: 500000, high: 1000000 },
    drawCalls: { low: 100, medium: 500, high: 1000 },
    memory: { low: 100, medium: 250, high: 500 }
  }
});

// 分析场景
const analysisResult = await optimizer.analyzeScene(scene);
console.log('场景评分:', analysisResult.overallScore);
console.log('优化建议:', analysisResult.suggestions);

// 自动优化场景
const optimizeSuccess = await optimizer.optimizeScene(scene);
console.log('优化结果:', optimizeSuccess ? '成功' : '失败');
```

### 优化建议类型

- **REDUCE_DRAW_CALLS**: 减少绘制调用
- **REDUCE_TRIANGLES**: 减少三角形数量
- **OPTIMIZE_TEXTURES**: 优化纹理
- **OPTIMIZE_MATERIALS**: 优化材质
- **ENABLE_INSTANCING**: 启用实例化
- **ENABLE_BATCHING**: 启用批处理
- **ENABLE_LOD**: 启用LOD
- **OPTIMIZE_LIGHTING**: 优化光照
- **OPTIMIZE_MEMORY**: 优化内存使用

## 在组件中使用

### 性能监控组件示例

```typescript
import React, { useState, useEffect } from 'react';
import { PerformanceMonitor, PerformanceMetricType } from '../libs/dl-engine-core';

const PerformancePanel: React.FC = () => {
  const [performanceData, setPerformanceData] = useState(null);

  useEffect(() => {
    const monitor = PerformanceMonitor.getInstance();
    monitor.configure({ enabled: true, autoSample: true });
    monitor.start();

    const interval = setInterval(() => {
      const report = monitor.getReport();
      setPerformanceData(report);
    }, 1000);

    return () => {
      clearInterval(interval);
      monitor.stop();
    };
  }, []);

  return (
    <div>
      {performanceData && (
        <div>
          <p>FPS: {performanceData.metrics.get(PerformanceMetricType.FPS)?.value}</p>
          <p>内存: {performanceData.metrics.get(PerformanceMetricType.MEMORY_USAGE)?.value}MB</p>
          <p>评分: {performanceData.overallScore}</p>
        </div>
      )}
    </div>
  );
};
```

### 场景优化组件示例

```typescript
import React, { useState } from 'react';
import { SceneOptimizer } from '../libs/dl-engine-core';

const SceneOptimizationPanel: React.FC = () => {
  const [optimizationResult, setOptimizationResult] = useState(null);
  const [isOptimizing, setIsOptimizing] = useState(false);

  const optimizeScene = async () => {
    setIsOptimizing(true);
    try {
      const optimizer = SceneOptimizer.getInstance();
      const result = await optimizer.analyzeScene();
      await optimizer.optimizeScene();
      setOptimizationResult(result);
    } catch (error) {
      console.error('优化失败:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  return (
    <div>
      <button onClick={optimizeScene} disabled={isOptimizing}>
        {isOptimizing ? '优化中...' : '优化场景'}
      </button>
      {optimizationResult && (
        <div>
          <p>评分: {optimizationResult.overallScore}</p>
          <p>建议数量: {optimizationResult.suggestions.length}</p>
        </div>
      )}
    </div>
  );
};
```

## 特性

### 性能监控器特性

- ✅ 实时性能监控
- ✅ 多种性能指标支持
- ✅ 历史数据记录
- ✅ 自动阈值检测
- ✅ 性能瓶颈识别
- ✅ 优化建议生成
- ✅ 可配置的采样间隔
- ✅ 调试模式支持

### 场景优化器特性

- ✅ 场景性能分析
- ✅ 智能优化建议
- ✅ 自动优化执行
- ✅ 可配置的优化阈值
- ✅ 多种优化策略
- ✅ 优化结果评估
- ✅ 调试信息输出
- ✅ 批量优化支持

## 注意事项

1. **单例模式**: 两个模块都使用单例模式，确保全局只有一个实例。
2. **异步操作**: 场景优化是异步操作，需要使用 async/await。
3. **内存管理**: 长时间运行时注意清理定时器和事件监听器。
4. **性能影响**: 监控本身会消耗一定性能，建议在生产环境中适当调整采样频率。
5. **浏览器兼容性**: 某些性能指标可能在不同浏览器中有差异。

## 扩展

如果需要添加新的性能指标或优化策略，可以：

1. 在 `PerformanceMetricType` 枚举中添加新的指标类型
2. 在 `OptimizationSuggestionType` 枚举中添加新的优化类型
3. 实现相应的收集和优化逻辑
4. 更新配置接口以支持新的选项

## 示例

完整的使用示例请参考 `examples/PerformanceMonitoringExample.tsx` 文件。
