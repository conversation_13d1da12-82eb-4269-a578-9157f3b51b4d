/**
 * 场景优化建议面板组件
 * 用于分析场景并提供优化建议
 */
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Typography, Space, Button, Tag, Collapse, Progress, Alert } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  BulbOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  RightOutlined} from '@ant-design/icons';
import { PerformanceMonitor, PerformanceMetricType } from '../../libs/dl-engine-core/utils/PerformanceMonitor';
import './SceneOptimizationPanel.less';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface OptimizationItem {
  key: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  status: 'good' | 'warning' | 'error';
  score: number;
  tips: string[];
}

interface SceneOptimizationProps {
  className?: string;
}

/**
 * 场景优化建议面板组件
 */
const SceneOptimizationPanel: React.FC<SceneOptimizationProps> = ({ className }) => {
  const { t } = useTranslation();
  
  // 状态
  const [optimizationItems, setOptimizationItems] = useState<OptimizationItem[]>([]);
  const [overallScore, setOverallScore] = useState<number>(0);
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  
  // 初始化
  useEffect(() => {
    analyzeScene();
  }, []);
  
  // 分析场景
  const analyzeScene = () => {
    setIsAnalyzing(true);
    
    // 模拟分析过程
    setTimeout(() => {
      // 从性能监控器获取数据
      const report = PerformanceMonitor.getInstance().getReport();
      const drawCalls = report.metrics.get(PerformanceMetricType.DRAW_CALLS)?.value || Math.floor(Math.random() * 1500) + 200;
      const triangles = report.metrics.get(PerformanceMetricType.TRIANGLES)?.value || Math.floor(Math.random() * 1500000) + 100000;
      const memoryUsage = report.metrics.get(PerformanceMetricType.MEMORY_USAGE)?.value || Math.floor(Math.random() * 800) + 100;
      
      // 生成优化项
      const items: OptimizationItem[] = [
        {
          key: '1',
          title: t('debug.optimization.drawCalls'),
          description: t('debug.optimization.drawCallsDesc'),
          severity: drawCalls > 1000 ? 'high' : drawCalls > 500 ? 'medium' : 'low',
          status: drawCalls > 1000 ? 'error' : drawCalls > 500 ? 'warning' : 'good',
          score: calculateScore(drawCalls, 2000, 100),
          tips: [
            t('debug.optimization.drawCallsTip1'),
            t('debug.optimization.drawCallsTip2'),
            t('debug.optimization.drawCallsTip3'),
          ]},
        {
          key: '2',
          title: t('debug.optimization.triangles'),
          description: t('debug.optimization.trianglesDesc'),
          severity: triangles > 1000000 ? 'high' : triangles > 500000 ? 'medium' : 'low',
          status: triangles > 1000000 ? 'error' : triangles > 500000 ? 'warning' : 'good',
          score: calculateScore(triangles, 2000000, 100000),
          tips: [
            t('debug.optimization.trianglesTip1'),
            t('debug.optimization.trianglesTip2'),
            t('debug.optimization.trianglesTip3'),
          ]},
        {
          key: '3',
          title: t('debug.optimization.textures'),
          description: t('debug.optimization.texturesDesc'),
          severity: 'medium',
          status: 'warning',
          score: 70,
          tips: [
            t('debug.optimization.texturesTip1'),
            t('debug.optimization.texturesTip2'),
            t('debug.optimization.texturesTip3'),
          ]},
        {
          key: '4',
          title: t('debug.optimization.lighting'),
          description: t('debug.optimization.lightingDesc'),
          severity: 'low',
          status: 'good',
          score: 85,
          tips: [
            t('debug.optimization.lightingTip1'),
            t('debug.optimization.lightingTip2'),
            t('debug.optimization.lightingTip3'),
          ]},
        {
          key: '5',
          title: t('debug.optimization.memory'),
          description: t('debug.optimization.memoryDesc'),
          severity: memoryUsage > 500 ? 'high' : memoryUsage > 250 ? 'medium' : 'low',
          status: memoryUsage > 500 ? 'error' : memoryUsage > 250 ? 'warning' : 'good',
          score: calculateScore(memoryUsage, 1000, 100),
          tips: [
            t('debug.optimization.memoryTip1'),
            t('debug.optimization.memoryTip2'),
            t('debug.optimization.memoryTip3'),
          ]},
      ];
      
      // 计算总体得分
      const totalScore = Math.round(
        items.reduce((sum, item) => sum + item.score, 0) / items.length
      );
      
      setOptimizationItems(items);
      setOverallScore(totalScore);
      setIsAnalyzing(false);
    }, 1000);
  };
  
  // 计算得分
  const calculateScore = (value: number, max: number, min: number): number => {
    const score = 100 - Math.min(100, Math.max(0, (value - min) / (max - min) * 100));
    return Math.round(score);
  };
  
  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good':
        return <CheckCircleOutlined className="optimization-good" />;
      case 'warning':
        return <WarningOutlined className="optimization-warning" />;
      case 'error':
        return <CloseCircleOutlined className="optimization-error" />;
      default:
        return null;
    }
  };
  
  // 获取严重程度标签
  const getSeverityTag = (severity: string) => {
    switch (severity) {
      case 'low':
        return <Tag color="green">{t('debug.optimization.low')}</Tag>;
      case 'medium':
        return <Tag color="orange">{t('debug.optimization.medium')}</Tag>;
      case 'high':
        return <Tag color="red">{t('debug.optimization.high')}</Tag>;
      default:
        return null;
    }
  };
  
  // 获取得分状态
  const getScoreStatus = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'normal';
    return 'exception';
  };
  
  return (
    <div className={`scene-optimization-panel ${className || ''}`}>
      <div className="optimization-toolbar">
        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={analyzeScene}
            loading={isAnalyzing}
          >
            {isAnalyzing ? t('debug.optimization.analyzing') : t('debug.optimization.analyze')}
          </Button>
        </Space>
      </div>
      
      <div className="optimization-content">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card>
              <div className="optimization-score">
                <Title level={4}>{t('debug.optimization.overallScore')}</Title>
                <Progress
                  type="circle"
                  percent={overallScore}
                  status={getScoreStatus(overallScore)}
                  format={(percent) => `${percent}`}
                  size={120}
                />
                <div className="optimization-score-text">
                  {overallScore >= 80 && (
                    <Alert
                      message={t('debug.optimization.scoreGood')}
                      type="success"
                      showIcon
                    />
                  )}
                  {overallScore >= 60 && overallScore < 80 && (
                    <Alert
                      message={t('debug.optimization.scoreWarning')}
                      type="warning"
                      showIcon
                    />
                  )}
                  {overallScore < 60 && (
                    <Alert
                      message={t('debug.optimization.scoreError')}
                      type="error"
                      showIcon
                    />
                  )}
                </div>
              </div>
            </Card>
          </Col>
        </Row>
        
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={24}>
            <Card title={t('debug.optimization.recommendations')}>
              <Collapse 
                bordered={false}
                expandIcon={({ isActive }) => <RightOutlined rotate={isActive ? 90 : 0} />}
              >
                {optimizationItems.map((item) => (
                  <Panel
                    key={item.key}
                    header={
                      <div className="optimization-item-header">
                        {getStatusIcon(item.status)}
                        <span className="optimization-item-title">{item.title}</span>
                        {getSeverityTag(item.severity)}
                        <Progress
                          percent={item.score}
                          status={getScoreStatus(item.score)}
                          size="small"
                          style={{ width: 100, marginLeft: 'auto' }}
                        />
                      </div>
                    }
                  >
                    <div className="optimization-item-content">
                      <Paragraph>{item.description}</Paragraph>
                      <Title level={5}>{t('debug.optimization.tips')}</Title>
                      <ul className="optimization-tips">
                        {item.tips.map((tip, index) => (
                          <li key={index}>
                            <BulbOutlined className="optimization-tip-icon" />
                            <Text>{tip}</Text>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </Panel>
                ))}
              </Collapse>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default SceneOptimizationPanel;
