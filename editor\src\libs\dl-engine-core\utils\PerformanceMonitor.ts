/**
 * 性能监控器
 * 用于监控和分析引擎性能
 */

/**
 * 性能指标类型
 */
export enum PerformanceMetricType {
  /** 帧率 */
  FPS = 'fps',
  /** 渲染时间 */
  RENDER_TIME = 'renderTime',
  /** 物理更新时间 */
  PHYSICS_TIME = 'physicsTime',
  /** 动画更新时间 */
  ANIMATION_TIME = 'animationTime',
  /** 输入处理时间 */
  INPUT_TIME = 'inputTime',
  /** 网络更新时间 */
  NETWORK_TIME = 'networkTime',
  /** 脚本执行时间 */
  SCRIPT_TIME = 'scriptTime',
  /** 总更新时间 */
  TOTAL_UPDATE_TIME = 'totalUpdateTime',
  /** 内存使用 */
  MEMORY_USAGE = 'memoryUsage',
  /** 纹理内存 */
  TEXTURE_MEMORY = 'textureMemory',
  /** 几何体内存 */
  GEOMETRY_MEMORY = 'geometryMemory',
  /** 绘制调用次数 */
  DRAW_CALLS = 'drawCalls',
  /** 三角形数量 */
  TRIANGLES = 'triangles',
  /** 顶点数量 */
  VERTICES = 'vertices',
  /** 碰撞对数量 */
  COLLISION_PAIRS = 'collisionPairs',
  /** 接触点数量 */
  CONTACT_POINTS = 'contactPoints',
  /** 实体数量 */
  ENTITY_COUNT = 'entityCount',
  /** 组件数量 */
  COMPONENT_COUNT = 'componentCount',
  /** 系统数量 */
  SYSTEM_COUNT = 'systemCount',
  /** 音频源数量 */
  AUDIO_SOURCE_COUNT = 'audioSourceCount',
  /** 光源数量 */
  LIGHT_COUNT = 'lightCount',
  /** 相机数量 */
  CAMERA_COUNT = 'cameraCount',
  /** 粒子数量 */
  PARTICLE_COUNT = 'particleCount',
  /** 网络连接数量 */
  NETWORK_CONNECTION_COUNT = 'networkConnectionCount',
  /** 网络消息数量 */
  NETWORK_MESSAGE_COUNT = 'networkMessageCount',
  /** 资源数量 */
  RESOURCE_COUNT = 'resourceCount',
  /** 加载中的资源数量 */
  LOADING_RESOURCE_COUNT = 'loadingResourceCount',
  /** 错误的资源数量 */
  ERROR_RESOURCE_COUNT = 'errorResourceCount',
  /** 事件数量 */
  EVENT_COUNT = 'eventCount',
  /** 垃圾回收次数 */
  GC_COUNT = 'gcCount',
  /** 垃圾回收时间 */
  GC_TIME = 'gcTime',
  /** GPU使用率 */
  GPU_USAGE = 'gpuUsage',
  /** CPU使用率 */
  CPU_USAGE = 'cpuUsage',
  /** 网络延迟 */
  NETWORK_LATENCY = 'networkLatency',
  /** 网络带宽 */
  NETWORK_BANDWIDTH = 'networkBandwidth'
}

/**
 * 性能指标
 */
export interface PerformanceMetric {
  /** 指标类型 */
  type: PerformanceMetricType;
  /** 指标名称 */
  name: string;
  /** 当前值 */
  value: number;
  /** 最小值 */
  min: number;
  /** 最大值 */
  max: number;
  /** 平均值 */
  average: number;
  /** 历史值 */
  history: number[];
  /** 历史长度限制 */
  historyLimit: number;
  /** 单位 */
  unit?: string;
  /** 阈值 */
  threshold?: number;
  /** 是否超过阈值 */
  exceedsThreshold?: boolean;
}

/**
 * 性能报告
 */
export interface PerformanceReport {
  /** 时间戳 */
  timestamp: number;
  /** 指标集合 */
  metrics: Map<PerformanceMetricType, PerformanceMetric>;
  /** 总体评分 */
  overallScore: number;
  /** 瓶颈列表 */
  bottlenecks: string[];
  /** 建议列表 */
  suggestions: string[];
}

/**
 * 性能监控配置
 */
export interface PerformanceMonitorConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 采样间隔（毫秒） */
  sampleInterval?: number;
  /** 历史长度限制 */
  historyLimit?: number;
  /** 是否启用自动采样 */
  autoSample?: boolean;
  /** 是否启用调试输出 */
  debug?: boolean;
  /** 是否启用性能警告 */
  enableWarnings?: boolean;
  /** 是否收集渲染指标 */
  collectRenderMetrics?: boolean;
  /** 是否收集物理指标 */
  collectPhysicsMetrics?: boolean;
  /** 是否收集内存指标 */
  collectMemoryMetrics?: boolean;
  /** 是否收集系统指标 */
  collectSystemMetrics?: boolean;
  /** 是否收集GPU指标 */
  collectGPUMetrics?: boolean;
  /** 是否收集CPU指标 */
  collectCPUMetrics?: boolean;
  /** 是否收集网络指标 */
  collectNetworkMetrics?: boolean;
  /** 是否收集资源指标 */
  collectResourceMetrics?: boolean;
  /** 是否收集事件指标 */
  collectEventMetrics?: boolean;
  /** 是否收集垃圾回收指标 */
  collectGCMetrics?: boolean;
}

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private config: PerformanceMonitorConfig;
  private metrics: Map<PerformanceMetricType, PerformanceMetric>;
  private running: boolean = false;
  private frameCount: number = 0;
  private lastFrameTime: number = 0;
  private sampleTimer: number | null = null;

  /**
   * 获取单例实例
   */
  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    this.config = {
      enabled: true,
      sampleInterval: 1000,
      historyLimit: 60,
      autoSample: true,
      debug: false,
      enableWarnings: true,
      collectRenderMetrics: true,
      collectPhysicsMetrics: true,
      collectMemoryMetrics: true,
      collectSystemMetrics: true,
      collectGPUMetrics: true,
      collectCPUMetrics: true,
      collectNetworkMetrics: true,
      collectResourceMetrics: true,
      collectEventMetrics: true,
      collectGCMetrics: true
    };

    this.metrics = new Map();
    this.initializeMetrics();
  }

  /**
   * 配置监控器
   */
  public configure(config: Partial<PerformanceMonitorConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (this.config.enabled && !this.running) {
      this.start();
    } else if (!this.config.enabled && this.running) {
      this.stop();
    }
  }

  /**
   * 启动监控
   */
  public start(): void {
    if (!this.config.enabled || this.running) {
      return;
    }

    this.running = true;
    this.frameCount = 0;
    this.lastFrameTime = performance.now();

    if (this.config.autoSample) {
      this.startAutoSampling();
    }

    if (this.config.debug) {
      console.log('PerformanceMonitor: 监控已启动');
    }
  }

  /**
   * 停止监控
   */
  public stop(): void {
    if (!this.running) {
      return;
    }

    this.running = false;
    
    if (this.sampleTimer) {
      clearInterval(this.sampleTimer);
      this.sampleTimer = null;
    }

    if (this.config.debug) {
      console.log('PerformanceMonitor: 监控已停止');
    }
  }

  /**
   * 获取报告
   */
  public getReport(): PerformanceReport {
    const bottlenecks: string[] = [];
    const suggestions: string[] = [];
    let totalScore = 0;
    let scoreCount = 0;

    // 分析指标并生成建议
    this.metrics.forEach((metric) => {
      if (metric.exceedsThreshold) {
        bottlenecks.push(`${metric.name} 超过阈值`);
        suggestions.push(`优化 ${metric.name}`);
      }

      // 计算评分（简化版本）
      if (metric.threshold && metric.value > 0) {
        const score = Math.max(0, 100 - (metric.value / metric.threshold) * 100);
        totalScore += score;
        scoreCount++;
      }
    });

    const overallScore = scoreCount > 0 ? totalScore / scoreCount : 100;

    return {
      timestamp: Date.now(),
      metrics: new Map(this.metrics),
      overallScore,
      bottlenecks,
      suggestions
    };
  }

  /**
   * 更新指标
   */
  public updateMetric(type: PerformanceMetricType, value: number): void {
    const metric = this.metrics.get(type);
    if (!metric) {
      return;
    }

    metric.value = value;
    metric.min = Math.min(metric.min, value);
    metric.max = Math.max(metric.max, value);
    
    // 更新历史
    metric.history.push(value);
    if (metric.history.length > metric.historyLimit) {
      metric.history.shift();
    }

    // 计算平均值
    metric.average = metric.history.reduce((sum, val) => sum + val, 0) / metric.history.length;

    // 检查阈值
    if (metric.threshold) {
      metric.exceedsThreshold = value > metric.threshold;
    }
  }

  /**
   * 采样
   */
  public sample(): void {
    if (!this.running) {
      return;
    }

    const now = performance.now();
    const elapsed = now - this.lastFrameTime;

    // 计算FPS
    if (elapsed > 0) {
      const fps = 1000 / elapsed;
      this.updateMetric(PerformanceMetricType.FPS, fps);
    }

    // 收集内存指标
    if (this.config.collectMemoryMetrics) {
      this.collectMemoryMetrics();
    }

    // 收集渲染指标
    if (this.config.collectRenderMetrics) {
      this.collectRenderMetrics();
    }

    this.frameCount++;
    this.lastFrameTime = now;
  }

  /**
   * 初始化指标
   */
  private initializeMetrics(): void {
    const metricTypes = Object.values(PerformanceMetricType);
    
    metricTypes.forEach(type => {
      this.metrics.set(type, {
        type,
        name: this.getMetricName(type),
        value: 0,
        min: Number.MAX_VALUE,
        max: Number.MIN_VALUE,
        average: 0,
        history: [],
        historyLimit: this.config.historyLimit || 60,
        unit: this.getMetricUnit(type),
        threshold: this.getMetricThreshold(type)
      });
    });
  }

  /**
   * 启动自动采样
   */
  private startAutoSampling(): void {
    if (this.sampleTimer) {
      clearInterval(this.sampleTimer);
    }

    this.sampleTimer = window.setInterval(() => {
      this.sample();
    }, this.config.sampleInterval || 1000);
  }

  /**
   * 收集内存指标
   */
  private collectMemoryMetrics(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.updateMetric(PerformanceMetricType.MEMORY_USAGE, memory.usedJSHeapSize / 1024 / 1024);
    }
  }

  /**
   * 收集渲染指标
   */
  private collectRenderMetrics(): void {
    // 模拟渲染指标（实际应用中应从渲染器获取）
    this.updateMetric(PerformanceMetricType.DRAW_CALLS, Math.floor(Math.random() * 1000) + 100);
    this.updateMetric(PerformanceMetricType.TRIANGLES, Math.floor(Math.random() * 1000000) + 50000);
  }

  /**
   * 获取指标名称
   */
  private getMetricName(type: PerformanceMetricType): string {
    const names: Record<PerformanceMetricType, string> = {
      [PerformanceMetricType.FPS]: '帧率',
      [PerformanceMetricType.RENDER_TIME]: '渲染时间',
      [PerformanceMetricType.PHYSICS_TIME]: '物理时间',
      [PerformanceMetricType.ANIMATION_TIME]: '动画时间',
      [PerformanceMetricType.INPUT_TIME]: '输入时间',
      [PerformanceMetricType.NETWORK_TIME]: '网络时间',
      [PerformanceMetricType.SCRIPT_TIME]: '脚本时间',
      [PerformanceMetricType.TOTAL_UPDATE_TIME]: '总更新时间',
      [PerformanceMetricType.MEMORY_USAGE]: '内存使用',
      [PerformanceMetricType.TEXTURE_MEMORY]: '纹理内存',
      [PerformanceMetricType.GEOMETRY_MEMORY]: '几何体内存',
      [PerformanceMetricType.DRAW_CALLS]: '绘制调用',
      [PerformanceMetricType.TRIANGLES]: '三角形数量',
      [PerformanceMetricType.VERTICES]: '顶点数量',
      [PerformanceMetricType.COLLISION_PAIRS]: '碰撞对',
      [PerformanceMetricType.CONTACT_POINTS]: '接触点',
      [PerformanceMetricType.ENTITY_COUNT]: '实体数量',
      [PerformanceMetricType.COMPONENT_COUNT]: '组件数量',
      [PerformanceMetricType.SYSTEM_COUNT]: '系统数量',
      [PerformanceMetricType.AUDIO_SOURCE_COUNT]: '音频源数量',
      [PerformanceMetricType.LIGHT_COUNT]: '光源数量',
      [PerformanceMetricType.CAMERA_COUNT]: '相机数量',
      [PerformanceMetricType.PARTICLE_COUNT]: '粒子数量',
      [PerformanceMetricType.NETWORK_CONNECTION_COUNT]: '网络连接数量',
      [PerformanceMetricType.NETWORK_MESSAGE_COUNT]: '网络消息数量',
      [PerformanceMetricType.RESOURCE_COUNT]: '资源数量',
      [PerformanceMetricType.LOADING_RESOURCE_COUNT]: '加载中资源数量',
      [PerformanceMetricType.ERROR_RESOURCE_COUNT]: '错误资源数量',
      [PerformanceMetricType.EVENT_COUNT]: '事件数量',
      [PerformanceMetricType.GC_COUNT]: '垃圾回收次数',
      [PerformanceMetricType.GC_TIME]: '垃圾回收时间',
      [PerformanceMetricType.GPU_USAGE]: 'GPU使用率',
      [PerformanceMetricType.CPU_USAGE]: 'CPU使用率',
      [PerformanceMetricType.NETWORK_LATENCY]: '网络延迟',
      [PerformanceMetricType.NETWORK_BANDWIDTH]: '网络带宽'
    };
    return names[type] || type;
  }

  /**
   * 获取指标单位
   */
  private getMetricUnit(type: PerformanceMetricType): string {
    const units: Partial<Record<PerformanceMetricType, string>> = {
      [PerformanceMetricType.FPS]: 'fps',
      [PerformanceMetricType.RENDER_TIME]: 'ms',
      [PerformanceMetricType.PHYSICS_TIME]: 'ms',
      [PerformanceMetricType.MEMORY_USAGE]: 'MB',
      [PerformanceMetricType.DRAW_CALLS]: '次',
      [PerformanceMetricType.TRIANGLES]: '个',
      [PerformanceMetricType.GPU_USAGE]: '%',
      [PerformanceMetricType.CPU_USAGE]: '%',
      [PerformanceMetricType.NETWORK_LATENCY]: 'ms',
      [PerformanceMetricType.NETWORK_BANDWIDTH]: 'MB/s'
    };
    return units[type] || '';
  }

  /**
   * 获取指标阈值
   */
  private getMetricThreshold(type: PerformanceMetricType): number | undefined {
    const thresholds: Partial<Record<PerformanceMetricType, number>> = {
      [PerformanceMetricType.FPS]: 30,
      [PerformanceMetricType.RENDER_TIME]: 16,
      [PerformanceMetricType.MEMORY_USAGE]: 500,
      [PerformanceMetricType.DRAW_CALLS]: 1000,
      [PerformanceMetricType.TRIANGLES]: 1000000
    };
    return thresholds[type];
  }
}
